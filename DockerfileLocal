# Use a specific version of the Node.js Alpine image
FROM node:23.1.0-alpine3.19

ENV TZ=America/Sao_Paulo
# Install Bash, and Nest CLI
RUN apk --no-cache add bash postgresql-client tzdata
RUN npm install -g @nestjs/cli

# Set the working directory inside the container
WORKDIR /user/src/app

# Copy all files into the container
COPY . .

# Copy the entrypoint script and give it the correct permissions
COPY entrypoint-prod.sh /user/src/app/entrypoint-prod.sh
RUN chmod +x /user/src/app/entrypoint-prod.sh

# Change the owner of the files to 'node'
RUN chown -R node:node .

# Switch to the 'node' user to run the application
USER node

# Setup npmrc to install private packages from GitHub Packages
ARG GITHUB_TOKEN
RUN npm config set @edutalent:registry https://npm.pkg.github.com/ && \
    echo "//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}" > ~/.npmrc

# Install dependencies and build the app
RUN npm ci

# Generate Prisma Client
RUN npx prisma generate

# Build the app
RUN npm run build

# Set the environment to production
ENV NODE_ENV=production

# Expose the port
EXPOSE 3000

# Use the entrypoint script as the default command for the container
ENTRYPOINT ["./entrypoint-local.sh"]
