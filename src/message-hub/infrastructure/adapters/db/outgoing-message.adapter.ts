import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { OutgoingMessagePort } from '@message-hub/infrastructure/ports/db/outgoing-message.port';
import { Prisma } from '@prisma/client';
import { randomUUID } from 'crypto';
import { CommunicationChannel } from '@common/enums';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { logger } from '@edutalent/commons-sdk';
import { DateTime } from 'luxon';

@Injectable()
export class OutgoingMessageAdapter
  extends PrismaCommonAdapter<OutgoingMessageEntity>
  implements OutgoingMessagePort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'outgoingMessage');
  }

  async insertOutgoingMessage(
    {
      customerId,
      from,
      to,
      messageType,
      message,
      channel,
      status,
      isFirstMessage,
      apiUrl,
      fileUrl,
    }: {
      customerId: string;
      from: string;
      to: string;
      messageType: string;
      message: string;
      channel: string;
      status: string;
      isFirstMessage: boolean;
      apiUrl: string;
      fileUrl: string;
    },
    randomDelay: number,
  ): Promise<void> {
    try {
      return this.prisma.client.$transaction(async tx => {
        // Get customer phone to check daily limit
        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: from, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(`Customer phone not found for number: ${from} and channel: ${channel}`);
        }

        // Get the latest scheduled message time
        const result = (await tx.$queryRaw(Prisma.sql`
          SELECT "time_to_go" as "maxTimeToGo"
          FROM "message_hub"."outgoing_message"
          WHERE communication_channel = ${channel}
            AND "from" = ${from}
          ORDER BY "time_to_go" DESC LIMIT 1
          FOR UPDATE
        `)) as { maxTimeToGo: Date }[];

        let newTimeToGo: DateTime;
        const lastTimeToGo = result[0]?.maxTimeToGo
          ? DateTime.fromJSDate(result[0].maxTimeToGo).setZone('America/Sao_Paulo')
          : DateTime.now().setZone('America/Sao_Paulo');
        const now = DateTime.now().setZone('America/Sao_Paulo');

        // If it's not a first message, send it immediately
        if (!isFirstMessage || process.env.NODE_ENV === 'development') {
          newTimeToGo = now.plus({ seconds: Number(randomDelay) });
        } else {
          // Set the time to go to the next business day at 8 AM Brazil time
          newTimeToGo = lastTimeToGo.plus({ seconds: Number(randomDelay) });

          // Function to check if a date is weekend
          const isWeekend = (date: DateTime): boolean => {
            const weekday = date.weekday; // 1=Monday, 7=Sunday
            const hour = date.hour;

            // Saturday (6) is weekend only after 14:00 (2 PM) Brazil time
            if (weekday === 6 && hour >= 14) {
              return true;
            }

            // Sunday (7) is always weekend
            if (weekday === 7) {
              return true;
            }

            return false;
          };

          // Function to get next business day
          const getNextBusinessDay = (date: DateTime): DateTime => {
            let nextDay = date.plus({ days: 1 });

            // Skip weekends - if next day is Saturday or Sunday, move to Monday
            while (isWeekend(nextDay)) {
              nextDay = nextDay.plus({ days: 1 });
            }

            // Set to 11:00 AM for the next business day
            return nextDay.set({ hour: 11, minute: 0, second: 0, millisecond: 0 });
          };

          // Function to count scheduled messages for a specific day
          const countScheduledMessagesForDay = async (date: DateTime): Promise<number> => {
            const startOfDay = date.startOf('day');
            const endOfDay = date.endOf('day');

            const [count] = (await tx.$queryRaw(Prisma.sql`
              SELECT COUNT(*) as count
              FROM "message_hub"."outgoing_message"
              WHERE "from" = ${from}
                AND "communication_channel" = ${channel}
                AND "is_first_message" = true
                AND "time_to_go" >= ${startOfDay.toISO()}::timestamp
                AND "time_to_go" <= ${endOfDay.toISO()}::timestamp
            `)) as [{ count: number }];

            return count.count;
          };

          // Check if we've reached the daily limit for the current day
          const currentDayCount = await countScheduledMessagesForDay(newTimeToGo);

          // Skip weekends
          if (isWeekend(newTimeToGo)) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
          }

          // If it's after 23:00, schedule for next business day
          if (newTimeToGo.hour >= 23) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
          }

          // If current day is full, find the next available day
          if (currentDayCount >= customerPhone.dailyLimit) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
          }

          // Now that we have a day with available spots, get the latest timeToGo for that day
          const startOfDay = newTimeToGo.startOf('day');
          const endOfDay = newTimeToGo.endOf('day');

          const latestMessage = (await tx.$queryRaw(Prisma.sql`
            SELECT "time_to_go" as "maxTimeToGo"
            FROM "message_hub"."outgoing_message"
            WHERE "sent" = false
              AND communication_channel = ${channel}
              AND "from" = ${from}
              AND "time_to_go" >= ${startOfDay.toISO()}::timestamp
              AND "time_to_go" <= ${endOfDay.toISO()}::timestamp
            ORDER BY "time_to_go" DESC LIMIT 1
            FOR UPDATE
          `)) as { maxTimeToGo: Date }[];

          // If there are no messages for this day yet, start from 11:00 AM
          if (!latestMessage || latestMessage.length === 0) {
            // First message of the day: schedule at exactly 11:00 AM (no random delay)
            newTimeToGo = newTimeToGo.set({ hour: 11, minute: 0, second: 0, millisecond: 0 });
          } else {
            // Subsequent messages: add random delay to the latest message time
            newTimeToGo = DateTime.fromJSDate(latestMessage[0].maxTimeToGo)
              .setZone('America/Sao_Paulo')
              .plus({ seconds: Number(randomDelay) });
          }
        }

        await tx.$executeRaw(
          Prisma.sql`
            INSERT INTO "message_hub"."outgoing_message" ("id", "customer_id", "to", "from", "message_type",
                                                          "message",
                                                          "communication_channel", "time_to_go", "sent",
                                                          "status", "created_at", "updated_at",
                                                          "api_url", "file_url", "is_first_message")
            VALUES (${randomUUID()}::uuid, ${customerId}::uuid, ${to}, ${from}, ${messageType}, ${message},
                    ${channel}, ${newTimeToGo.toISO()}::timestamp, false,
                    ${status}, ${now.toISO()}::timestamp, ${now.toISO()}::timestamp, ${apiUrl}, ${fileUrl}, ${isFirstMessage})
          `,
        );
      });
    } catch (error) {
      throw new Error(`Error inserting outgoing message: ${error}`);
    }
  }

  async processOutgoingMessage(
    fromNumber: string,
    channel: CommunicationChannel,
    sendMessage: (to: string, message: string, apiUrl: string) => Promise<void>,
    sendMessageWithFile: (
      to: string,
      message: string,
      apiUrl: string,
      fileUrl: string,
      fileType: string,
    ) => Promise<void>,
    sendMessageSMS: (from: string, to: string, text: string, apiUrl: string) => Promise<void>,
  ): Promise<void> {
    const now = DateTime.now().setZone('America/Sao_Paulo');
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "time_to_go" <= ${now}::timestamp
            AND "from" = ${fromNumber}
            AND "communication_channel" = ${channel}
          ORDER BY "time_to_go" ASC LIMIT 1
              FOR
          UPDATE SKIP LOCKED
        `)) as {
          id: string;
          from: string;
          to: string;
          message: string;
          timeToGo: Date;
          channel: string;
          apiUrl: string;
          messageType: string;
          fileUrl?: string;
        }[];

        if (!messages || messages.length === 0) {
          return;
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: fromNumber, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for number: ${fromNumber} and channel: ${channel}`,
          );
        }

        const maxOutgoingDelay = customerPhone.outgoingMaxDelay;

        for (const message of messages) {
          const startSendingDate = DateTime.now().setZone('America/Sao_Paulo');
          try {
            if (message.channel === CommunicationChannel.WHATSAPPSELFHOSTED) {
              if (message.fileUrl) {
                await sendMessageWithFile(
                  message.to,
                  message.message,
                  message.apiUrl,
                  message.fileUrl,
                  message.messageType,
                );
              } else {
                await sendMessage(message.to, message.message, message.apiUrl);
              }
            } else if (message.channel === CommunicationChannel.SMS_VONAGE) {
              await sendMessageSMS(message.from, message.to, message.message, message.apiUrl);
            } else {
              throw new BusinessException(
                'Outgoing-Message-Adapter',
                `Channel ${message.channel} not supported for number: ${fromNumber}`,
                BusinessExceptionStatus.INVALID_INPUT,
              );
            }

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "sent"    = true,
                    "sent_at" = ${now.toISO()}::timestamp,
                WHERE "id" = ${message.id}::uuid
              `,
            );
            const finishedSendingDate = DateTime.now().setZone('America/Sao_Paulo');
            logger.info(
              `Message sent successfully from phone: ${customerPhone.phoneNumber} to: ${message.to
              }. Took: ${finishedSendingDate.diff(startSendingDate, 'milliseconds').milliseconds
              }ms. Message: ${JSON.stringify(message)}`,
            );
          } catch (error) {
            const newTimeToGo = DateTime.fromJSDate(message.timeToGo)
              .setZone('America/Sao_Paulo')
              .plus({ seconds: Number(maxOutgoingDelay) * 5 });

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "time_to_go" = ${newTimeToGo.toISO()}::timestamp
                WHERE "id" = ${message.id}::uuid
              `,
            );

            const finishedSendingDate = DateTime.now().setZone('America/Sao_Paulo');
            logger.error(
              `Failed to send message from phone: ${customerPhone.phoneNumber} to: ${message.to
              }. Took: ${finishedSendingDate.diff(startSendingDate, 'milliseconds').milliseconds
              }ms. Message: ${JSON.stringify(message)}.Error: ${error.message}`,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getPendingOutgoingMessage(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
  ): Promise<OutgoingMessageEntity[]> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "customer_id" = ${Prisma.sql`${customerId}::uuid`}
            AND "to" = ${to}
            AND "communication_channel" = ${channel}
          ORDER BY "time_to_go" ASC
            FOR
              UPDATE SKIP LOCKED
        `)) as OutgoingMessageEntity[];

        if (!messages || messages.length === 0) {
          // logger.info(
          //   `No pending messages to send for customer: ${customerId} and channel: ${channel}...`,
          // );
          return [];
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { customerId: customerId, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for customer: ${customerId} and channel: ${channel}`,
          );
        }

        await tx.$executeRaw(
          Prisma.sql`
            UPDATE message_hub.outgoing_message om
            SET sent       = true,
                sent_at    = NOW(),
                updated_at = NOW()
            WHERE om.id IN (${Prisma.join(
            messages.map(message => Prisma.sql`${message.id}::uuid`),
          )});
          `,
        );

        return messages;
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getTimestampFromDatabase(): Promise<{ now: Date; nowToString: string }> {
    return this.prisma.client.$transaction(async tx => {
      const [currentDatabaseTime] = (await tx.$queryRaw(Prisma.sql`
            SELECT now() as now
        `)) as [{ now: Date }];
      return { now: currentDatabaseTime.now, nowToString: currentDatabaseTime.now.toString() };
    });
  }

  async getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{ date: string; count: number; total: number }[]> {
    const startDateTime = DateTime.fromJSDate(dateStart).setZone('America/Sao_Paulo');
    const endDateTime = DateTime.fromJSDate(dateEnd).setZone('America/Sao_Paulo');

    const results = await this.prisma.client.$queryRaw<{ date: string; count: number }[]>(
      Prisma.sql`
        SELECT TO_CHAR(DATE(om.sent_at), 'YYYY-MM-DD') as date,
               COUNT(*) as count
        FROM message_hub.outgoing_message om
        INNER JOIN business_base.portfolio_item pi ON om.to = pi.phone_number
        WHERE om.customer_id = ${customerId}::uuid
          AND pi.portfolio_id = ${portfolioId}::uuid
          AND om.sent = true
          AND om.is_first_message = true
          AND om.sent_at >= ${startDateTime.toISO()}::timestamp
          AND om.sent_at <= ${endDateTime.toISO()}::timestamp
          AND pi.status = 'ACTIVE'
          AND om.status != 'DELETED'
        GROUP BY DATE(om.sent_at)
        ORDER BY DATE(om.sent_at) ASC
      `,
    );

    // Calculate total count
    const total = results.reduce((sum, row) => sum + Number(row.count), 0);

    // Return results with total included in each row for consistency
    return results.map(row => ({
      date: row.date,
      count: Number(row.count),
      total,
    }));
  }
}
