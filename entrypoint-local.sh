#!/bin/sh

echo "Starting the application in production"
# Set the DATABASE_URL to the migration user
export DATABASE_URL=$DATABASE_URL_MIGRATE
USER=$DATABASE_USER_ROLE
DB_URL=$DB_MIGRATE_CONNECTION
SCHEMA_DIR="./prisma/schema"

# Run migrations
npx prisma migrate deploy --schema=$SCHEMA_DIR

# Execute GRANT command in PostgreSQL using environment variables
SCHEMAS=$(ls -d $SCHEMA_DIR/*/ | xargs -n 1 basename)

for SCHEMA in $SCHEMAS; do
  echo "Granting permissions for schema: $SCHEMA to user: $USER"

  # Grant USAGE on the schema
  psql $DB_URL -c "GRANT USAGE ON SCHEMA $SCHEMA TO $USER;"

  # Grant permissions on all existing tables
  psql $DB_URL -c "GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA $SCHEMA TO $USER;"

  # Grant permissions on all existing sequences
  psql $DB_URL -c "GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA $SCHEMA TO $USER;"

done

# Set the DATABASE_URL to the application user
export DATABASE_URL=$DATABASE_URL_USER

# Start de APP
npm run start:debug
