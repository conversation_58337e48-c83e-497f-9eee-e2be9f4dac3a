{"name": "transcendence", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prisma:check-schemas": "node scripts/check-schemas.js", "prisma:check-failed-migrations": "node scripts/check-prisma-migration.js", "local-infra:seed": "node scripts/db_seeds/local-seed.js", "test": "jest --runInBand --config ./jest.config.json --forceExit", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e-ci": "jest --runInBand --config ./jest.config.json --forceExit", "test:e2e-ci-verbose": "jest --runInBand --config ./jest.config.json --forceExit --verbose", "test:e2e": "jest --runInBand --config ./test/e2e/jest.config.ts --forceExit", "test:unit": "jest --config ./test/unit/jest.config.json --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "local-infra:start": "TZ=UTC bash scripts/run-local-infra.sh", "clean": "rm -rf node_modules && rm -f package-lock.json && npm cache clean --force", "reinstall": "npm run clean && npm install", "dependencies:check-exact": "node scripts/check-exact-dependencies.js", "generate:system-token": "node scripts/generate-system-token.js"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.693.0", "@aws-sdk/client-s3": "3.693.0", "@aws-sdk/client-sqs": "3.693.0", "@aws-sdk/credential-providers": "3.828.0", "@aws-sdk/lib-dynamodb": "3.693.0", "@aws-sdk/lib-storage": "3.693.0", "@edutalent/commons-sdk": "0.0.33", "@langchain/community": "0.3.15", "@langchain/core": "0.3.18", "@langchain/openai": "0.3.14", "@nestjs/axios": "3.1.2", "@nestjs/common": "10.4.8", "@nestjs/core": "10.4.8", "@nestjs/jwt": "10.2.0", "@nestjs/mapped-types": "2.0.6", "@nestjs/platform-express": "10.4.8", "@nestjs/schedule": "4.1.1", "@nestjs/serve-static": "4.0.2", "@nestjs/swagger": "8.0.7", "@nestjs/websockets": "10.4.8", "@opensearch-project/opensearch": "3.5.1", "@prisma/client": "5.21.1", "@types/luxon": "3.7.1", "amazon-s3-uri": "0.1.1", "aws-sdk": "2.1691.0", "axios": "1.7.4", "bcrypt": "5.1.1", "class-transformer": "0.5.1", "class-validator": "0.14.1", "cookie": "1.0.2", "csv-writer": "1.6.0", "date-and-time": "3.6.0", "dotenv": "16.4.5", "elevenlabs": "0.15.0", "fast-csv": "5.0.2", "js-yaml": "4.1.0", "langchain": "0.3.6", "luxon": "3.7.1", "newrelic": "12.21.0", "node-schedule": "2.1.1", "openai": "4.52.7", "reflect-metadata": "0.2.0", "rxjs": "7.8.1", "soap": "1.1.7", "sqs-consumer": "11.2.0", "stream-concat": "2.0.0", "zod": "3.24.1"}, "devDependencies": {"@nestjs/cli": "10.4.7", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "10.4.8", "@types/async": "3.2.24", "@types/express": "4.17.17", "@types/jest": "29.5.2", "@types/multer": "1.4.12", "@types/newrelic": "9.14.8", "@types/node": "20.3.1", "@types/node-schedule": "2.1.7", "@types/sqs-consumer": "5.0.0", "@types/supertest": "6.0.0", "@typescript-eslint/eslint-plugin": "7.16.0", "@typescript-eslint/parser": "7.15.0", "db": "6.0.3", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "5.1.3", "jest": "29.7.0", "nock": "13.5.6", "prettier": "3.0.0", "prisma": "5.21.1", "source-map-support": "0.5.21", "supertest": "6.3.3", "ts-jest": "29.1.0", "ts-loader": "9.4.3", "ts-node": "10.9.1", "tsconfig-paths": "4.2.0", "typescript": "5.5.3"}}