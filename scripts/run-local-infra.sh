#!/bin/bash
set -e

# Function to stop and clean up the services
echo "Stopping and cleaning up the database service..."
docker-compose -f docker-compose-local.yml down -v
echo "Cleanup completed."

echo "Starting services..."
docker-compose -f docker-compose-local.yml up -d postgres opensearch localstack

# Wait for the database to be ready (adjust the time as needed)
echo "Waiting for the database to be ready..."
sleep 10

export DATABASE_URL="postgresql://postgres:password123@localhost:5442/transcendence_db"
export NODE_ENV="local"

echo "Deploying Prisma migrations to the test database..."
npx prisma generate
npx prisma migrate deploy --schema=./prisma/schema

echo "Local infrastructure started"
echo "Configuring localstack..."
export AWS_ACCESS_KEY_ID="test"
export AWS_SECRET_ACCESS_KEY="test"
export AWS_REGION="us-east-1"
export AWS_ENDPOINT_URL="http://localhost:4566"
export AWS_PAGER=""
export LOG_GROUP_NAME_TRANSCENDENCE="transcendence-staging"
export LOG_STREAM_NAME_TRANSCENDENCE="transcendence-staging"
export PORTFOLIO_IMPORT_FIDELEASY_QUEUE_URL="http://localhost:4566/000000000000/fideleasy_portfolio_import"
export PORTFOLIO_IMPORT_SALESZAP_QUEUE_URL="http://localhost:4566/000000000000/saleszap_portfolio_import"
export PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL="http://localhost:4566/000000000000/collectcash_portfolio_import"
export PORTFOLIO_WORKFLOW_FIDELEASY_QUEUE_URL="http://localhost:4566/000000000000/fideleasy_portfolio_workflow"
export PORTFOLIO_WORKFLOW_SALESZAP_QUEUE_URL="http://localhost:4566/000000000000/saleszap_portfolio_workflow"
export PORTFOLIO_WORKFLOW_COLLECTCASH_QUEUE_URL="http://localhost:4566/000000000000/collectcash_portfolio_workflow"
export DEFAULT_PORTFOLIO_PROCESSING_RATE_LIMIT=100
export DEFAULT_PORTFOLIO_IDLE_AFTER=7
export OUTGOING_MESSAGE_QUEUE_URL="http://localhost:4566/000000000000/message_outgoing"
export INCOMING_MESSAGE_QUEUE_URL="http://localhost:4566/000000000000/message_incoming"
export DATA_INSIGHTS_VECTOR_STORE_HIRING_QUEUE_URL="http://localhost:4566/000000000000/data_insights_vector_store_hiring"
export MAX_DELAY_BETWEEN_MESSAGES=30
export BUSINESS_BASE_SERVICE_URL="http://localhost:3000"
export MESSAGEHUB_SERVICE_URL="http://localhost:3000"
export TRANSCENDENCE_ADMIN_WEB_URL="http://localhost:3390"
export SLACK_TOKEN= "slack_token_fake"
export VONAGE_API_KEY="ebe08b1c"
export VONAGE_API_SECRET="kqWCQ2kHErHLorEL"
export VONAGE_SIGNATURE_SECRET=""
export DIRECT_MESSAGE_FILES_BUCKET="transcendence-direct-message-files"
export PORTFOLIO_IMPORT_FILES_BUCKET="transcendence-portfolio-import-files"
IMPORT_ITEM_QUEUE_PREFIX="portfolio-item-"
IMPORT_ITEM_QUEUE_SUFIX="sufix"
SQS_QUEUE_BASE_URL="http://localhost:4566/000000000000/"

echo "Creating DynamoDB tables..."

echo "Workflow..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/workflow-definition.json

echo "Workflow Execution..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/workflow-execution-definition.json

echo "Message history..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/message-history-definition.json

echo "Portfolio item custom data..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/portfolio-item-custom-data-definition.json

echo "Customer channel integration data..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/customer-channel-integration-data-definition.json

echo "Conversation message..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/conversation-message-definition.json

echo "Middleware response output..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/middleware-response-output-definition.json

echo "Customer preferences..."
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/customer-preferences-definition.json

echo "Dynamo tables created!"

echo "Creating S3 buckets..."
aws s3api create-bucket --bucket transcendence-direct-message-files --region us-east-1
aws s3api create-bucket --bucket transcendence-portfolio-import-files --region us-east-1


echo "Creating SQS queues..."
aws sqs create-queue --queue-name fideleasy_portfolio_import
aws sqs create-queue --queue-name saleszap_portfolio_import
aws sqs create-queue --queue-name collectcash_portfolio_import

aws sqs create-queue --queue-name fideleasy_portfolio_workflow 
aws sqs create-queue --queue-name saleszap_portfolio_workflow 
aws sqs create-queue --queue-name collectcash_portfolio_workflow 

aws sqs create-queue --queue-name message_outgoing
aws sqs create-queue --queue-name message_incoming

aws sqs create-queue --queue-name data_insights_vector_store_hiring

echo "Seeding the database with initial data..."
npm run local-infra:seed


echo "Local infrastructure started!"





